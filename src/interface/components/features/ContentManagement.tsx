/**
 * ContentManagement Component
 * 
 * Feature component for managing content (videos/photos) in the Interface Layer.
 * Uses data hooks from Interface Layer for React Query integration.
 * Follows 3-layer MVI architecture principles.
 * 
 * Architecture Rules:
 * - Feature component belongs to Interface Layer
 * - Uses data hooks (useContent, useContentStats) for data management
 * - Uses Zod validation through data hooks
 * - No direct repository access (goes through hooks)
 * - Responsive design with Tailwind CSS
 */

import { useState } from 'react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import { 
  Video,
  Image,
  Search,
  Filter,
  MoreHorizontal,
  Play,
  Download,
  Trash2,
  Eye,
  Clock,
  User,
  Calendar,
  Upload,
  Edit,
  Share
} from 'lucide-react';

// Import data hooks from Interface Layer
import {
  useContent,
  useContentStats,
  useContentMutations,
  useContentByType,
} from '../../hooks/data';

import type { ContentSearchParams } from '../../../models';

const ContentManagement = () => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================
  
  const [searchTerm, setSearchTerm] = useState('');
  const [contentFilter, setContentFilter] = useState<'all' | 'video' | 'photo'>('all');
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<'all' | 'completed' | 'processing' | 'failed'>('all');

  // ============================================================================
  // DATA HOOKS
  // ============================================================================

  // Search parameters for content
  const searchParams: ContentSearchParams = {
    searchTerm: searchTerm || undefined,
    type: contentFilter === 'all' ? undefined : contentFilter,
    // status: statusFilter === 'all' ? undefined : statusFilter,
    page: currentPage,
    limit: 12, // Grid layout works better with 12 items
    sortBy: 'createdAt',
    sortOrder: 'desc',
  };

  // Fetch content with React Query
  const {
    content,
    total,
    page,
    totalPages,
    isLoading: contentLoading,
    isError: contentError,
    error: contentErrorMessage,
    refetch: refetchContent,
    hasNextPage,
    hasPreviousPage,
  } = useContent(searchParams, {
    staleTime: 3 * 60 * 1000, // 3 minutes
    refetchInterval: 60 * 1000, // Refresh every minute for processing status
  });

  // Content statistics
  const {
    data: contentStats,
    isLoading: statsLoading,
  } = useContentStats({
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Content mutations
  const {
    deleteContent,
    updateContent,
    bulkDeleteContent,
  } = useContentMutations();

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleContentFilter = (filter: 'all' | 'video' | 'photo') => {
    setContentFilter(filter);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleStatusFilter = (status: 'all' | 'completed' | 'processing' | 'failed') => {
    setStatusFilter(status);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleSelectItem = (id: number) => {
    setSelectedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === content.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(content.map(item => item.id));
    }
  };

  const handleDeleteContent = async (id: number) => {
    try {
      await deleteContent.mutateAsync(id);
      console.log('Content deleted successfully');
    } catch (error) {
      console.error('Failed to delete content:', error);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return;

    try {
      await bulkDeleteContent.mutateAsync(selectedItems);
      setSelectedItems([]);
      console.log('Content deleted successfully');
    } catch (error) {
      console.error('Failed to delete content:', error);
    }
  };

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Hoàn thành';
      case 'processing': return 'Đang xử lý';
      case 'failed': return 'Thất bại';
      default: return 'Không xác định';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // ============================================================================
  // LOADING AND ERROR STATES
  // ============================================================================

  if (contentLoading) {
    return (
      <div className="spacing-responsive">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-2 text-slate-600">Đang tải nội dung...</p>
          </div>
        </div>
      </div>
    );
  }

  if (contentError) {
    return (
      <div className="spacing-responsive">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600">Lỗi khi tải nội dung: {contentErrorMessage?.message}</p>
            <Button 
              onClick={() => refetchContent()} 
              className="mt-4"
              variant="outline"
            >
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const totalVideos = contentStats?.totalVideos || 0;
  const totalPhotos = contentStats?.totalPhotos || 0;
  const totalViews = contentStats?.totalViews || 0;
  const processingCount = contentStats?.processingCount || 0;

  // ============================================================================
  // RENDER COMPONENT
  // ============================================================================

  return (
    <div className="spacing-responsive">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h2 className="text-xl sm:text-2xl font-bold text-slate-900">Quản lý nội dung</h2>
          <p className="text-sm sm:text-base text-slate-600">
            Quản lý tất cả videos và photos được tạo
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {selectedItems.length > 0 && (
            <Button
              variant="outline"
              onClick={handleBulkDelete}
              disabled={bulkDeleteContent.isLoading}
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Xóa ({selectedItems.length})
            </Button>
          )}
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Xuất danh sách</span>
            <span className="sm:hidden">Xuất</span>
          </Button>
          <Button className="bg-indigo-600 hover:bg-indigo-700">
            <Upload className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Tải lên</span>
            <span className="sm:hidden">Tải</span>
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid-responsive-1-2-4 gap-4 sm:gap-6">
        <Card className="p-4 sm:p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Video className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-slate-600">Tổng Videos</p>
              <p className="text-lg sm:text-2xl font-bold text-slate-900">
                {statsLoading ? '...' : totalVideos.toLocaleString()}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4 sm:p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Image className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-slate-600">Tổng Photos</p>
              <p className="text-lg sm:text-2xl font-bold text-slate-900">
                {statsLoading ? '...' : totalPhotos.toLocaleString()}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4 sm:p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Eye className="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-slate-600">Tổng lượt xem</p>
              <p className="text-lg sm:text-2xl font-bold text-slate-900">
                {statsLoading ? '...' : totalViews.toLocaleString()}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4 sm:p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Clock className="w-5 h-5 sm:w-6 sm:h-6 text-orange-600" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-slate-600">Đang xử lý</p>
              <p className="text-lg sm:text-2xl font-bold text-slate-900">
                {statsLoading ? '...' : processingCount}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <Input
                placeholder="Tìm kiếm theo tiêu đề hoặc người tạo..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2 flex-wrap">
            <Button
              variant={contentFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleContentFilter('all')}
            >
              Tất cả
            </Button>
            <Button
              variant={contentFilter === 'video' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleContentFilter('video')}
            >
              Videos
            </Button>
            <Button
              variant={contentFilter === 'photo' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleContentFilter('photo')}
            >
              Photos
            </Button>
            <Button
              variant={statusFilter === 'completed' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleStatusFilter('completed')}
            >
              Hoàn thành
            </Button>
            <Button
              variant={statusFilter === 'processing' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleStatusFilter('processing')}
            >
              Đang xử lý
            </Button>
          </div>
        </div>

        {/* Bulk Actions */}
        {content.length > 0 && (
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={selectedItems.length === content.length}
                  onChange={handleSelectAll}
                  className="rounded border-slate-300"
                />
                <span className="text-sm text-slate-600">
                  Chọn tất cả ({content.length})
                </span>
              </label>
              {selectedItems.length > 0 && (
                <span className="text-sm text-slate-600">
                  Đã chọn {selectedItems.length} mục
                </span>
              )}
            </div>
          </div>
        )}

        {/* Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
          {content.map((item) => (
            <div key={item.id} className="bg-white border border-slate-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
              <div className="relative">
                <img
                  src={item.thumbnail || '/placeholder.svg'}
                  alt={item.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-2 left-2">
                  {item.type === 'video' ? (
                    <Badge className="bg-blue-600 text-white">
                      <Video className="w-3 h-3 mr-1" />
                      Video
                    </Badge>
                  ) : (
                    <Badge className="bg-green-600 text-white">
                      <Image className="w-3 h-3 mr-1" />
                      Photo
                    </Badge>
                  )}
                </div>
                <div className="absolute top-2 right-2">
                  <Badge className={getStatusColor(item.status)}>
                    {getStatusText(item.status)}
                  </Badge>
                </div>
                {item.type === 'video' && item.duration && (
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
                    {item.duration}
                  </div>
                )}
                <div className="absolute top-2 left-2">
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(item.id)}
                    onChange={() => handleSelectItem(item.id)}
                    className="rounded border-slate-300"
                  />
                </div>
              </div>

              <div className="p-4">
                <h3 className="font-medium text-slate-900 mb-2 line-clamp-2">{item.title}</h3>

                <div className="flex items-center space-x-2 text-sm text-slate-500 mb-3">
                  <User className="w-3 h-3" />
                  <span className="truncate">{item.creator?.name || 'Unknown'}</span>
                </div>

                <div className="flex items-center justify-between text-sm text-slate-500 mb-3">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-3 h-3" />
                    <span>{new Date(item.createdAt).toLocaleDateString('vi-VN')}</span>
                  </div>
                  <span>{formatFileSize(item.fileSize || 0)}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <Eye className="w-3 h-3 text-slate-400" />
                      <span>{item.views || 0}</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {item.creditsUsed || 0} credits
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="ghost">
                      <Eye className="w-3 h-3" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Download className="w-3 h-3" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Share className="w-3 h-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDeleteContent(item.id)}
                      disabled={deleteContent.isLoading}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {content.length === 0 && !contentLoading && (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Video className="w-8 h-8 text-slate-400" />
            </div>
            <h3 className="text-lg font-medium text-slate-900 mb-2">Không có nội dung</h3>
            <p className="text-slate-600 mb-4">
              {searchTerm || contentFilter !== 'all' || statusFilter !== 'all'
                ? 'Không tìm thấy nội dung phù hợp với bộ lọc hiện tại.'
                : 'Chưa có nội dung nào được tạo.'
              }
            </p>
            <Button className="bg-indigo-600 hover:bg-indigo-700">
              <Upload className="w-4 h-4 mr-2" />
              Tạo nội dung đầu tiên
            </Button>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6">
            <p className="text-sm text-slate-600">
              Hiển thị {((page - 1) * 12) + 1} - {Math.min(page * 12, total)} của {total} nội dung
            </p>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(page - 1)}
                disabled={!hasPreviousPage}
              >
                Trước
              </Button>
              <span className="text-sm text-slate-600">
                Trang {page} / {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(page + 1)}
                disabled={!hasNextPage}
              >
                Sau
              </Button>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default ContentManagement;
