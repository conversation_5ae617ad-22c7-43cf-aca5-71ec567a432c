/**
 * SystemSettings Component
 * 
 * Feature component for system settings management in the Interface Layer.
 * Uses data hooks from Interface Layer for React Query integration.
 * Follows 3-layer MVI architecture principles.
 * 
 * Architecture Rules:
 * - Feature component belongs to Interface Layer
 * - Uses data hooks (useSystemConfig, useSystemMutations) for data management
 * - Uses Zod validation through data hooks
 * - No direct repository access (goes through hooks)
 * - Responsive design with Tailwind CSS
 */

import { useState } from 'react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Switch } from '../ui/switch';
import { 
  Settings,
  Save,
  RefreshCw,
  Shield,
  Database,
  Mail,
  Bell,
  CreditCard,
  Users,
  Video,
  Image,
  Globe,
  Lock,
  Key,
  Server,
  Monitor,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';

// Import data hooks from Interface Layer
import {
  useSystemConfig,
  useSystemStatus,
  useFeatureFlags,
  useSystemMutations,
  useMaintenanceSettings,
  useSystemLimits,
} from '../../hooks/data';

import type { SystemConfig, FeatureFlag, MaintenanceSettings } from '../../../models';

const SystemSettings = () => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================
  
  const [activeTab, setActiveTab] = useState('general');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // ============================================================================
  // DATA HOOKS
  // ============================================================================

  // System configuration
  const {
    data: systemConfig,
    isLoading: configLoading,
    isError: configError,
    error: configErrorMessage,
    refetch: refetchConfig,
  } = useSystemConfig({
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // System status
  const {
    data: systemStatus,
    isLoading: statusLoading,
  } = useSystemStatus({
    refetchInterval: 30 * 1000, // Refresh every 30 seconds
  });

  // Feature flags
  const {
    data: featureFlags,
    isLoading: flagsLoading,
  } = useFeatureFlags({
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Maintenance settings
  const {
    data: maintenanceSettings,
    isLoading: maintenanceLoading,
  } = useMaintenanceSettings({
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // System limits
  const {
    data: systemLimits,
    isLoading: limitsLoading,
  } = useSystemLimits({
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // System mutations
  const {
    updateConfig,
    updateFeatureFlag,
    updateMaintenanceSettings,
    updateSystemLimits,
    restartService,
    clearCache,
  } = useSystemMutations();

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleConfigChange = (key: string, value: any) => {
    setHasUnsavedChanges(true);
    // TODO: Update local state
  };

  const handleSaveConfig = async () => {
    try {
      // TODO: Implement save logic
      await updateConfig.mutateAsync({
        // Updated config data
      });
      setHasUnsavedChanges(false);
      console.log('Configuration saved successfully');
    } catch (error) {
      console.error('Failed to save configuration:', error);
    }
  };

  const handleFeatureFlagToggle = async (flagId: string, enabled: boolean) => {
    try {
      await updateFeatureFlag.mutateAsync({
        id: flagId,
        enabled,
      });
      console.log('Feature flag updated successfully');
    } catch (error) {
      console.error('Failed to update feature flag:', error);
    }
  };

  const handleRestartService = async (serviceName: string) => {
    try {
      await restartService.mutateAsync(serviceName);
      console.log('Service restarted successfully');
    } catch (error) {
      console.error('Failed to restart service:', error);
    }
  };

  const handleClearCache = async () => {
    try {
      await clearCache.mutateAsync();
      console.log('Cache cleared successfully');
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  };

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return CheckCircle;
      case 'warning': return AlertTriangle;
      case 'error': return AlertTriangle;
      default: return Info;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'healthy': return 'Hoạt động tốt';
      case 'warning': return 'Cảnh báo';
      case 'error': return 'Lỗi';
      default: return 'Không xác định';
    }
  };

  // ============================================================================
  // LOADING AND ERROR STATES
  // ============================================================================

  if (configLoading) {
    return (
      <div className="spacing-responsive">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-2 text-slate-600">Đang tải cài đặt hệ thống...</p>
          </div>
        </div>
      </div>
    );
  }

  if (configError) {
    return (
      <div className="spacing-responsive">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600">Lỗi khi tải cài đặt: {configErrorMessage?.message}</p>
            <Button 
              onClick={() => refetchConfig()} 
              className="mt-4"
              variant="outline"
            >
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // ============================================================================
  // RENDER COMPONENT
  // ============================================================================

  return (
    <div className="spacing-responsive">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h2 className="text-xl sm:text-2xl font-bold text-slate-900">Cài đặt hệ thống</h2>
          <p className="text-sm sm:text-base text-slate-600">
            Quản lý cấu hình và thiết lập hệ thống
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {hasUnsavedChanges && (
            <Badge variant="outline" className="text-orange-600 border-orange-200">
              Có thay đổi chưa lưu
            </Badge>
          )}
          <Button 
            variant="outline"
            onClick={handleClearCache}
            disabled={clearCache.isLoading}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Xóa cache</span>
            <span className="sm:hidden">Cache</span>
          </Button>
          <Button 
            onClick={handleSaveConfig}
            disabled={!hasUnsavedChanges || updateConfig.isLoading}
            className="bg-indigo-600 hover:bg-indigo-700"
          >
            <Save className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Lưu thay đổi</span>
            <span className="sm:hidden">Lưu</span>
          </Button>
        </div>
      </div>

      {/* System Status Overview */}
      <Card className="p-4 sm:p-6">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Trạng thái hệ thống</h3>
        <div className="grid-responsive-1-2-4 gap-4">
          {systemStatus?.services?.map((service, index) => {
            const StatusIcon = getStatusIcon(service.status);
            return (
              <div key={index} className="flex items-center space-x-3 p-3 bg-slate-50 rounded-lg">
                <StatusIcon className={`w-5 h-5 ${getStatusColor(service.status)}`} />
                <div className="min-w-0 flex-1">
                  <p className="font-medium text-slate-900">{service.name}</p>
                  <p className={`text-sm ${getStatusColor(service.status)}`}>
                    {getStatusText(service.status)}
                  </p>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleRestartService(service.name)}
                  disabled={restartService.isLoading}
                >
                  <RefreshCw className="w-3 h-3" />
                </Button>
              </div>
            );
          })}
        </div>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">Chung</TabsTrigger>
          <TabsTrigger value="features">Tính năng</TabsTrigger>
          <TabsTrigger value="limits">Giới hạn</TabsTrigger>
          <TabsTrigger value="maintenance">Bảo trì</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card className="p-4 sm:p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">Cài đặt chung</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Tên ứng dụng
                </label>
                <Input
                  value={systemConfig?.appName || ''}
                  onChange={(e) => handleConfigChange('appName', e.target.value)}
                  placeholder="Mega AI Admin"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  URL cơ sở
                </label>
                <Input
                  value={systemConfig?.baseUrl || ''}
                  onChange={(e) => handleConfigChange('baseUrl', e.target.value)}
                  placeholder="https://admin.megaai.com"
                />
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="features" className="space-y-6">
          <Card className="p-4 sm:p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">Tính năng</h3>
            <div className="space-y-4">
              {featureFlags?.map((flag) => (
                <div key={flag.id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div>
                    <p className="font-medium text-slate-900">{flag.name}</p>
                    <p className="text-sm text-slate-600">{flag.description}</p>
                  </div>
                  <Switch
                    checked={flag.enabled}
                    onCheckedChange={(enabled) => handleFeatureFlagToggle(flag.id, enabled)}
                  />
                </div>
              ))}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="limits" className="space-y-6">
          <p className="text-slate-600">Cài đặt giới hạn hệ thống sẽ được hiển thị ở đây</p>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-6">
          <p className="text-slate-600">Cài đặt bảo trì sẽ được hiển thị ở đây</p>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SystemSettings;
